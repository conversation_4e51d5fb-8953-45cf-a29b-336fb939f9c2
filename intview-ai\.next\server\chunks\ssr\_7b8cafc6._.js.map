{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,8OAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAoC;sDAClC,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;uCAEe", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["type QuestionsListProps = {\r\n  currentQuestion?: number;\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  currentQuestion = 1,\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,kBAAkB,CAAC,EACnB,SAAS,EACU;IACnB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,8OAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,8OAAC;gCAAK,WAAU;;;;;;0CAElB,8OAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,8OAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;uCAEe", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/DIDAgent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { <PERSON><PERSON>, Loader2 } from \"lucide-react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nconst DID_API_URL = \"https://api.d-id.com\";\r\n\r\ninterface DIDAgentProps {\r\n  className?: string;\r\n  instructions?: string;\r\n  agentName?: string;\r\n}\r\n\r\ninterface Agent {\r\n  id: string;\r\n  preview_name: string;\r\n  status: string;\r\n  presenter: {\r\n    type: string;\r\n    voice: {\r\n      type: string;\r\n      voice_id: string;\r\n    };\r\n    thumbnail: string;\r\n    source_url: string;\r\n  };\r\n  llm: {\r\n    type: string;\r\n    provider: string;\r\n    model: string;\r\n    instructions: string;\r\n  };\r\n}\r\n\r\nconst DIDAgent: React.FC<DIDAgentProps> = ({\r\n  className = \"\",\r\n  instructions = \"You are an AI interview assistant designed to conduct professional interviews.\",\r\n  agentName = \"Interview Assistant\",\r\n}) => {\r\n  const [agent, setAgent] = useState<Agent | null>(null);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);\r\n\r\n  const getAuthHeaders = () => {\r\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\r\n    console.log(\"Using D-ID API Key:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"NOT_FOUND\");\r\n\r\n    return {\r\n      \"Authorization\": `Basic ${apiKey}`,\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n  };\r\n\r\n  const createAgent = useCallback(async () => {\r\n    setIsCreatingAgent(true);\r\n    setError(null);\r\n\r\n    const payload = {\r\n      presenter: {\r\n        type: \"talk\",\r\n        voice: {\r\n          type: \"microsoft\",\r\n          voice_id: \"en-US-JennyMultilingualV2Neural\"\r\n        },\r\n        thumbnail: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\r\n        source_url: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\"\r\n      },\r\n      llm: {\r\n        type: \"openai\",\r\n        provider: \"openai\",\r\n        model: \"gpt-4o-mini\",\r\n        instructions: instructions\r\n      },\r\n      preview_name: agentName\r\n    };\r\n\r\n    try {\r\n      console.log(\"Creating D-ID Agent with payload:\", payload);\r\n\r\n      const response = await fetch(`${DID_API_URL}/agents`, {\r\n        method: \"POST\",\r\n        headers: getAuthHeaders(),\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      console.log(\"D-ID Agent API Response Status:\", response.status);\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        console.error(\"D-ID Agent API Error Response:\", errorText);\r\n        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);\r\n      }\r\n\r\n      const agentData: Agent = await response.json();\r\n      console.log(\"D-ID Agent Created Successfully:\", agentData);\r\n      console.log(\"Agent Presenter Data:\", agentData.presenter);\r\n      console.log(\"Avatar Thumbnail URL:\", agentData.presenter?.thumbnail);\r\n      setAgent(agentData);\r\n    } catch (err: unknown) {\r\n      console.error(\"D-ID Agent Creation Error:\", err);\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create agent\";\r\n      setError(`Agent Creation Failed: ${errorMessage}`);\r\n\r\n      // Log additional context for debugging\r\n      console.log(\"D-ID API URL:\", `${DID_API_URL}/agents`);\r\n      console.log(\"Request payload:\", JSON.stringify(payload, null, 2));\r\n    } finally {\r\n      setIsCreatingAgent(false);\r\n    }\r\n  }, [instructions, agentName]);\r\n\r\n  // Initialize agent on component mount\r\n  useEffect(() => {\r\n    if (!agent && !isCreatingAgent) {\r\n      createAgent();\r\n    }\r\n  }, [agent, isCreatingAgent, createAgent]);\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      <AnimatePresence>\r\n        {isCreatingAgent && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"absolute inset-0 bg-gray-100 rounded-lg flex flex-col items-center justify-center z-10\"\r\n          >\r\n            <Loader2 className=\"w-8 h-8 animate-spin text-blue-600 mb-2\" />\r\n            <p className=\"text-sm text-gray-600\">\r\n              Creating AI Agent...\r\n            </p>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {error && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"absolute top-2 left-2 right-2 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm z-20\"\r\n        >\r\n          {error}\r\n        </motion.div>\r\n      )}\r\n\r\n      <div className=\"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex flex-col items-center justify-center overflow-hidden\">\r\n        {agent ? (\r\n          <div className=\"text-center w-full h-full flex flex-col\">\r\n            {/* Avatar Image */}\r\n            <div className=\"flex-1 flex items-center justify-center p-4\">\r\n              {agent.presenter?.thumbnail ? (\r\n                <Image\r\n                  src={agent.presenter.thumbnail}\r\n                  alt={agent.preview_name}\r\n                  width={320}\r\n                  height={320}\r\n                  className=\"w-full h-full object-cover rounded-lg shadow-lg max-w-xs max-h-80\"\r\n                  onError={(e) => {\r\n                    console.error(\"Failed to load avatar image:\", agent.presenter.thumbnail);\r\n                    // Fallback to bot icon if image fails to load\r\n                    e.currentTarget.style.display = 'none';\r\n                    e.currentTarget.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              {/* Fallback Bot Icon */}\r\n              <div className={`w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center ${agent.presenter?.thumbnail ? 'hidden' : ''}`}>\r\n                <Bot className=\"w-10 h-10 text-white\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Agent Info */}\r\n            <div className=\"bg-white/90 backdrop-blur-sm p-3 rounded-t-lg\">\r\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">\r\n                {agent.preview_name}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600\">\r\n                AI Agent Ready\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"text-center p-4\">\r\n            <div className=\"w-20 h-20 bg-red-300 rounded-full flex items-center justify-center mb-4 mx-auto\">\r\n              <Bot className=\"w-10 h-10 text-red-600\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-red-600 mb-2\">\r\n              Agent Creation Failed\r\n            </h3>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              Using fallback mode\r\n            </p>\r\n            <button\r\n              onClick={createAgent}\r\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors\"\r\n            >\r\n              Retry\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center\">\r\n            <div className=\"w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mb-4 mx-auto\">\r\n              <Bot className=\"w-10 h-10 text-gray-500\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">\r\n              Initializing Agent...\r\n            </h3>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DIDAgent;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AAAA;AAJA;;;;;;AAMA,MAAM,cAAc;AA6BpB,MAAM,WAAoC,CAAC,EACzC,YAAY,EAAE,EACd,eAAe,gFAAgF,EAC/F,YAAY,qBAAqB,EAClC;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEhE,MAAM,iBAAiB;QACrB,MAAM,SAAS,4FAAuC,QAAQ,GAAG,CAAC,WAAW,IAAI;QACjF,QAAQ,GAAG,CAAC,uBAAuB,uCAAS,GAAG,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;QAE3E,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,QAAQ;YAClC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,mBAAmB;QACnB,SAAS;QAET,MAAM,UAAU;YACd,WAAW;gBACT,MAAM;gBACN,OAAO;oBACL,MAAM;oBACN,UAAU;gBACZ;gBACA,WAAW;gBACX,YAAY;YACd;YACA,KAAK;gBACH,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,cAAc;YAChB;YACA,cAAc;QAChB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,OAAO,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,MAAM,YAAmB,MAAM,SAAS,IAAI;YAC5C,QAAQ,GAAG,CAAC,oCAAoC;YAChD,QAAQ,GAAG,CAAC,yBAAyB,UAAU,SAAS;YACxD,QAAQ,GAAG,CAAC,yBAAyB,UAAU,SAAS,EAAE;YAC1D,SAAS;QACX,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,uBAAuB,EAAE,cAAc;YAEjD,uCAAuC;YACvC,QAAQ,GAAG,CAAC,iBAAiB,GAAG,YAAY,OAAO,CAAC;YACpD,QAAQ,GAAG,CAAC,oBAAoB,KAAK,SAAS,CAAC,SAAS,MAAM;QAChE,SAAU;YACR,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAc;KAAU;IAE5B,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,CAAC,iBAAiB;YAC9B;QACF;IACF,GAAG;QAAC;QAAO;QAAiB;KAAY;IAExC,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAO1C,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAET;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACZ,sBACC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,SAAS,EAAE,0BAChB,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,SAAS,CAAC,SAAS;oCAC9B,KAAK,MAAM,YAAY;oCACvB,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,SAAS,CAAC;wCACR,QAAQ,KAAK,CAAC,gCAAgC,MAAM,SAAS,CAAC,SAAS;wCACvE,8CAA8C;wCAC9C,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wCAChC,EAAE,aAAa,CAAC,kBAAkB,EAAE,UAAU,OAAO;oCACvD;;;;;2CAEA;8CAEJ,8OAAC;oCAAI,WAAW,CAAC,oEAAoE,EAAE,MAAM,SAAS,EAAE,YAAY,WAAW,IAAI;8CACjI,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,MAAM,YAAY;;;;;;8CAErB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;2BAKvC,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;yCAKH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;;;;;;;;;;;;;;;;;;AAQrE;uCAEe", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/hooks/useDIDStreaming.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from \"react\";\n\nconst DID_API_URL = \"https://api.d-id.com\";\n\ninterface StreamSession {\n  id: string;\n  session_id: string;\n  offer: RTCSessionDescriptionInit;\n  ice_servers: RTCIceServer[];\n}\n\ninterface UseDIDStreamingOptions {\n  avatarImageUrl?: string;\n  autoStart?: boolean;\n  onStreamReady?: () => void;\n  onStreamEnd?: () => void;\n}\n\nexport const useDIDStreaming = ({\n  avatarImageUrl = \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\n  autoStart = true,\n  onStreamReady,\n  onStreamEnd,\n}: UseDIDStreamingOptions = {}) => {\n  const [isConnecting, setIsConnecting] = useState<boolean>(false);\n  const [isConnected, setIsConnected] = useState<boolean>(false);\n  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [connectionState, setConnectionState] = useState<string>(\"new\");\n\n  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);\n  const streamSessionRef = useRef<StreamSession | null>(null);\n  const videoElementRef = useRef<HTMLVideoElement | null>(null);\n\n  const getAuthHeaders = useCallback(() => {\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\n    return {\n      \"Authorization\": `Basic ${apiKey}`,\n      \"Content-Type\": \"application/json\",\n    };\n  }, []);\n\n  // Create streaming session\n  const createStreamSession = useCallback(async (): Promise<StreamSession> => {\n    const response = await fetch(`${DID_API_URL}/talks/streams`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        source_url: avatarImageUrl,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to create stream session: ${response.statusText}`);\n    }\n\n    return await response.json();\n  }, [avatarImageUrl, getAuthHeaders]);\n\n  // Handle ICE candidates\n  const handleIceCandidate = useCallback((event: RTCPeerConnectionIceEvent) => {\n    if (event.candidate && streamSessionRef.current) {\n      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;\n      \n      fetch(`${DID_API_URL}/talks/streams/${streamSessionRef.current.id}/ice`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify({\n          candidate,\n          sdpMid,\n          sdpMLineIndex,\n          session_id: streamSessionRef.current.session_id,\n        }),\n      }).catch(console.error);\n    }\n  }, [getAuthHeaders]);\n\n  // Handle connection state changes\n  const handleConnectionStateChange = useCallback(() => {\n    if (peerConnectionRef.current) {\n      const state = peerConnectionRef.current.connectionState;\n      setConnectionState(state);\n      \n      if (state === \"connected\") {\n        setIsConnected(true);\n        setIsConnecting(false);\n        onStreamReady?.();\n      } else if (state === \"failed\" || state === \"closed\") {\n        setIsConnected(false);\n        setIsConnecting(false);\n        onStreamEnd?.();\n      }\n    }\n  }, [onStreamReady, onStreamEnd]);\n\n  // Handle incoming video track\n  const handleTrack = useCallback((event: RTCTrackEvent) => {\n    if (videoElementRef.current && event.streams[0]) {\n      videoElementRef.current.srcObject = event.streams[0];\n      \n      // Auto-play with error handling\n      videoElementRef.current.play().catch((err) => {\n        console.warn(\"Auto-play failed:\", err);\n      });\n    }\n  }, []);\n\n  // Create WebRTC peer connection\n  const createPeerConnection = useCallback(async (session: StreamSession) => {\n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n    }\n\n    const peerConnection = new RTCPeerConnection({ \n      iceServers: session.ice_servers,\n      iceCandidatePoolSize: 10,\n    });\n    \n    // Add event listeners\n    peerConnection.addEventListener(\"icecandidate\", handleIceCandidate);\n    peerConnection.addEventListener(\"connectionstatechange\", handleConnectionStateChange);\n    peerConnection.addEventListener(\"track\", handleTrack);\n\n    // Set remote description and create answer\n    await peerConnection.setRemoteDescription(session.offer);\n    const answer = await peerConnection.createAnswer();\n    await peerConnection.setLocalDescription(answer);\n\n    peerConnectionRef.current = peerConnection;\n\n    // Send answer to server\n    const response = await fetch(`${DID_API_URL}/talks/streams/${session.id}/sdp`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        answer: answer,\n        session_id: session.session_id,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to send SDP answer: ${response.statusText}`);\n    }\n\n    return peerConnection;\n  }, [handleIceCandidate, handleConnectionStateChange, handleTrack, getAuthHeaders]);\n\n  // Start streaming\n  const startStreaming = useCallback(async () => {\n    if (isConnecting || isConnected) return;\n\n    setIsConnecting(true);\n    setError(null);\n\n    try {\n      const session = await createStreamSession();\n      streamSessionRef.current = session;\n      await createPeerConnection(session);\n    } catch (err: any) {\n      console.error(\"Streaming setup error:\", err);\n      setError(err.message || \"Failed to start streaming\");\n      setIsConnecting(false);\n    }\n  }, [isConnecting, isConnected, createStreamSession, createPeerConnection]);\n\n  // Speak text with real-time lip-sync\n  const speakText = useCallback(async (textToSpeak: string) => {\n    if (!isConnected || !streamSessionRef.current || !textToSpeak.trim()) {\n      return;\n    }\n\n    setIsSpeaking(true);\n\n    try {\n      const response = await fetch(`${DID_API_URL}/talks/streams/${streamSessionRef.current.id}`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify({\n          script: {\n            type: \"text\",\n            input: textToSpeak.trim(),\n            provider: \"microsoft\",\n            voice_id: \"en-US-JennyMultilingualV2Neural\",\n          },\n          config: {\n            stitch: true,\n            fluent: true,\n            pad_audio: 0.0,\n          },\n          session_id: streamSessionRef.current.session_id,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to send text: ${response.statusText}`);\n      }\n\n      // Calculate speaking duration based on text length and speaking rate\n      const wordsPerMinute = 150;\n      const words = textToSpeak.trim().split(/\\s+/).length;\n      const speakingDuration = Math.max((words / wordsPerMinute) * 60 * 1000, 1500);\n      \n      setTimeout(() => {\n        setIsSpeaking(false);\n      }, speakingDuration);\n\n    } catch (err: any) {\n      console.error(\"Speaking error:\", err);\n      setIsSpeaking(false);\n      setError(err.message || \"Failed to speak text\");\n    }\n  }, [isConnected, getAuthHeaders]);\n\n  // Stop streaming\n  const stopStreaming = useCallback(async () => {\n    if (streamSessionRef.current) {\n      try {\n        await fetch(`${DID_API_URL}/talks/streams/${streamSessionRef.current.id}`, {\n          method: \"DELETE\",\n          headers: getAuthHeaders(),\n          body: JSON.stringify({\n            session_id: streamSessionRef.current.session_id,\n          }),\n        });\n      } catch (err) {\n        console.error(\"Error stopping stream:\", err);\n      }\n    }\n\n    // Clean up WebRTC connection\n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Clean up video element\n    if (videoElementRef.current && videoElementRef.current.srcObject) {\n      const stream = videoElementRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      videoElementRef.current.srcObject = null;\n    }\n\n    streamSessionRef.current = null;\n    setIsConnected(false);\n    setIsConnecting(false);\n    setIsSpeaking(false);\n    setConnectionState(\"new\");\n    onStreamEnd?.();\n  }, [getAuthHeaders, onStreamEnd]);\n\n  // Auto-start effect\n  useEffect(() => {\n    if (autoStart && !isConnecting && !isConnected) {\n      startStreaming();\n    }\n  }, [autoStart, isConnecting, isConnected, startStreaming]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      stopStreaming();\n    };\n  }, [stopStreaming]);\n\n  return {\n    isConnecting,\n    isConnected,\n    isSpeaking,\n    error,\n    connectionState,\n    videoElementRef,\n    startStreaming,\n    stopStreaming,\n    speakText,\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc;AAgBb,MAAM,kBAAkB,CAAC,EAC9B,iBAAiB,iFAAiF,EAClG,YAAY,IAAI,EAChB,aAAa,EACb,WAAW,EACY,GAAG,CAAC,CAAC;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4B;IAC3D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAExD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,MAAM,SAAS,4FAAuC,QAAQ,GAAG,CAAC,WAAW,IAAI;QACjF,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,QAAQ;YAClC,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,cAAc,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;YACd;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,GAAG;QAAC;QAAgB;KAAe;IAEnC,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,IAAI,MAAM,SAAS,IAAI,iBAAiB,OAAO,EAAE;YAC/C,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,SAAS;YAE5D,MAAM,GAAG,YAAY,eAAe,EAAE,iBAAiB,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBACvE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA,YAAY,iBAAiB,OAAO,CAAC,UAAU;gBACjD;YACF,GAAG,KAAK,CAAC,QAAQ,KAAK;QACxB;IACF,GAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9C,IAAI,kBAAkB,OAAO,EAAE;YAC7B,MAAM,QAAQ,kBAAkB,OAAO,CAAC,eAAe;YACvD,mBAAmB;YAEnB,IAAI,UAAU,aAAa;gBACzB,eAAe;gBACf,gBAAgB;gBAChB;YACF,OAAO,IAAI,UAAU,YAAY,UAAU,UAAU;gBACnD,eAAe;gBACf,gBAAgB;gBAChB;YACF;QACF;IACF,GAAG;QAAC;QAAe;KAAY;IAE/B,8BAA8B;IAC9B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,gBAAgB,OAAO,IAAI,MAAM,OAAO,CAAC,EAAE,EAAE;YAC/C,gBAAgB,OAAO,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,EAAE;YAEpD,gCAAgC;YAChC,gBAAgB,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBACpC,QAAQ,IAAI,CAAC,qBAAqB;YACpC;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC9C,IAAI,kBAAkB,OAAO,EAAE;YAC7B,kBAAkB,OAAO,CAAC,KAAK;QACjC;QAEA,MAAM,iBAAiB,IAAI,kBAAkB;YAC3C,YAAY,QAAQ,WAAW;YAC/B,sBAAsB;QACxB;QAEA,sBAAsB;QACtB,eAAe,gBAAgB,CAAC,gBAAgB;QAChD,eAAe,gBAAgB,CAAC,yBAAyB;QACzD,eAAe,gBAAgB,CAAC,SAAS;QAEzC,2CAA2C;QAC3C,MAAM,eAAe,oBAAoB,CAAC,QAAQ,KAAK;QACvD,MAAM,SAAS,MAAM,eAAe,YAAY;QAChD,MAAM,eAAe,mBAAmB,CAAC;QAEzC,kBAAkB,OAAO,GAAG;QAE5B,wBAAwB;QACxB,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,eAAe,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;YACT,MAAM,KAAK,SAAS,CAAC;gBACnB,QAAQ;gBACR,YAAY,QAAQ,UAAU;YAChC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;QACrE;QAEA,OAAO;IACT,GAAG;QAAC;QAAoB;QAA6B;QAAa;KAAe;IAEjF,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,gBAAgB,aAAa;QAEjC,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,iBAAiB,OAAO,GAAG;YAC3B,MAAM,qBAAqB;QAC7B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,IAAI,OAAO,IAAI;YACxB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAc;QAAa;QAAqB;KAAqB;IAEzE,qCAAqC;IACrC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI,CAAC,eAAe,CAAC,iBAAiB,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI;YACpE;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,eAAe,EAAE,iBAAiB,OAAO,CAAC,EAAE,EAAE,EAAE;gBAC1F,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;wBACN,MAAM;wBACN,OAAO,YAAY,IAAI;wBACvB,UAAU;wBACV,UAAU;oBACZ;oBACA,QAAQ;wBACN,QAAQ;wBACR,QAAQ;wBACR,WAAW;oBACb;oBACA,YAAY,iBAAiB,OAAO,CAAC,UAAU;gBACjD;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;YAC/D;YAEA,qEAAqE;YACrE,MAAM,iBAAiB;YACvB,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;YACpD,MAAM,mBAAmB,KAAK,GAAG,CAAC,AAAC,QAAQ,iBAAkB,KAAK,MAAM;YAExE,WAAW;gBACT,cAAc;YAChB,GAAG;QAEL,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,cAAc;YACd,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF,GAAG;QAAC;QAAa;KAAe;IAEhC,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,iBAAiB,OAAO,EAAE;YAC5B,IAAI;gBACF,MAAM,MAAM,GAAG,YAAY,eAAe,EAAE,iBAAiB,OAAO,CAAC,EAAE,EAAE,EAAE;oBACzE,QAAQ;oBACR,SAAS;oBACT,MAAM,KAAK,SAAS,CAAC;wBACnB,YAAY,iBAAiB,OAAO,CAAC,UAAU;oBACjD;gBACF;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;QAEA,6BAA6B;QAC7B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,kBAAkB,OAAO,CAAC,KAAK;YAC/B,kBAAkB,OAAO,GAAG;QAC9B;QAEA,yBAAyB;QACzB,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,CAAC,SAAS,EAAE;YAChE,MAAM,SAAS,gBAAgB,OAAO,CAAC,SAAS;YAChD,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,gBAAgB,OAAO,CAAC,SAAS,GAAG;QACtC;QAEA,iBAAiB,OAAO,GAAG;QAC3B,eAAe;QACf,gBAAgB;QAChB,cAAc;QACd,mBAAmB;QACnB;IACF,GAAG;QAAC;QAAgB;KAAY;IAEhC,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,CAAC,gBAAgB,CAAC,aAAa;YAC9C;QACF;IACF,GAAG;QAAC;QAAW;QAAc;QAAa;KAAe;IAEzD,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/DIDStreamingAvatar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { Bot, Loader2 } from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useDIDStreaming } from \"@/hooks/useDIDStreaming\";\n\ninterface DIDStreamingAvatarProps {\n  text?: string;\n  onStreamReady?: () => void;\n  onStreamEnd?: () => void;\n  className?: string;\n  isLoading?: boolean;\n  autoStart?: boolean;\n  avatarImageUrl?: string;\n}\n\nconst DIDStreamingAvatar: React.FC<DIDStreamingAvatarProps> = ({\n  text,\n  onStreamReady,\n  onStreamEnd,\n  className = \"\",\n  isLoading = false,\n  autoStart = true,\n  avatarImageUrl = \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\n}) => {\n  const pendingTextRef = useRef<string>(\"\");\n\n  const {\n    isConnecting,\n    isConnected,\n    isSpeaking,\n    error,\n    connectionState,\n    videoElementRef,\n    startStreaming,\n    stopStreaming,\n    speakText,\n  } = useDIDStreaming({\n    avatarImageUrl,\n    autoStart,\n    onStreamReady,\n    onStreamEnd,\n  });\n\n\n\n  // Effect to handle text changes\n  useEffect(() => {\n    if (text && text.trim() && isConnected) {\n      speakText(text);\n    } else if (text && text.trim() && !isConnected) {\n      // Store text to speak once connected\n      pendingTextRef.current = text;\n    }\n  }, [text, isConnected, speakText]);\n\n  // Effect to speak pending text once connected\n  useEffect(() => {\n    if (isConnected && pendingTextRef.current) {\n      speakText(pendingTextRef.current);\n      pendingTextRef.current = \"\";\n    }\n  }, [isConnected, speakText]);\n\n  return (\n    <div className={`relative w-full h-full ${className}`}>\n      <AnimatePresence mode=\"wait\">\n        {isConnected && !error ? (\n          <motion.div\n            key=\"video\"\n            initial={{ scale: 0.95, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.95, opacity: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"w-full h-full relative\"\n          >\n            <video\n              ref={videoElementRef}\n              autoPlay\n              playsInline\n              muted={false}\n              className=\"w-full h-full object-cover rounded-lg\"\n            />\n\n            {/* Speaking indicator */}\n            <AnimatePresence>\n              {isSpeaking && (\n                <motion.div\n                  initial={{ scale: 0, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0, opacity: 0 }}\n                  className=\"absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2\"\n                >\n                  <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                  <span className=\"text-white text-sm font-medium\">Speaking</span>\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            {/* Connection status indicator */}\n            <div className=\"absolute bottom-4 left-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2\">\n              <div className={`w-2 h-2 rounded-full ${\n                connectionState === \"connected\" ? \"bg-green-400\" :\n                connectionState === \"connecting\" ? \"bg-yellow-400 animate-pulse\" :\n                \"bg-red-400\"\n              }`}></div>\n              <span className=\"text-white text-xs capitalize\">{connectionState}</span>\n            </div>\n\n            {/* Live streaming indicator */}\n            <div className=\"absolute top-4 left-4 flex items-center space-x-2 bg-red-500/80 backdrop-blur-sm rounded-full px-3 py-2\">\n              <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n              <span className=\"text-white text-xs font-medium\">LIVE</span>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"loading\"\n            initial={{ scale: 0.95, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.95, opacity: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg\"\n          >\n            {error ? (\n              <>\n                <Bot className=\"w-16 h-16 text-red-400 mb-4\" />\n                <p className=\"text-red-600 text-center text-sm mb-4\">{error}</p>\n                <button\n                  onClick={startStreaming}\n                  className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n                >\n                  Retry Connection\n                </button>\n              </>\n            ) : (\n              <>\n                <div className=\"relative mb-4\">\n                  <Bot className=\"w-16 h-16 text-blue-400\" />\n                  {(isConnecting || isLoading) && (\n                    <Loader2 className=\"w-6 h-6 text-blue-500 animate-spin absolute -top-1 -right-1\" />\n                  )}\n                </div>\n                <p className=\"text-gray-600 text-center text-sm\">\n                  {isConnecting ? \"Connecting to live avatar...\" : \"Initializing live streaming...\"}\n                </p>\n                <p className=\"text-gray-500 text-center text-xs mt-2\">\n                  Real-time lip-sync enabled\n                </p>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default DIDStreamingAvatar;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAAA;AACA;AAJA;;;;;;AAgBA,MAAM,qBAAwD,CAAC,EAC7D,IAAI,EACJ,aAAa,EACb,WAAW,EACX,YAAY,EAAE,EACd,YAAY,KAAK,EACjB,YAAY,IAAI,EAChB,iBAAiB,iFAAiF,EACnG;IACC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAEtC,MAAM,EACJ,YAAY,EACZ,WAAW,EACX,UAAU,EACV,KAAK,EACL,eAAe,EACf,eAAe,EACf,cAAc,EACd,aAAa,EACb,SAAS,EACV,GAAG,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE;QAClB;QACA;QACA;QACA;IACF;IAIA,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,KAAK,IAAI,MAAM,aAAa;YACtC,UAAU;QACZ,OAAO,IAAI,QAAQ,KAAK,IAAI,MAAM,CAAC,aAAa;YAC9C,qCAAqC;YACrC,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC;QAAM;QAAa;KAAU;IAEjC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,eAAe,OAAO,EAAE;YACzC,UAAU,eAAe,OAAO;YAChC,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,qBACE,8OAAC;QAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW;kBACnD,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,MAAK;sBACnB,eAAe,CAAC,sBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,OAAO;oBAAM,SAAS;gBAAE;gBACnC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAM,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,WAAU;;;;;;kCAIZ,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,MAAM;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAC7B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;kCAMvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,qBAAqB,EACpC,oBAAoB,cAAc,iBAClC,oBAAoB,eAAe,gCACnC,cACA;;;;;;0CACF,8OAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;;eA3C/C;;;;qCA+CN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,OAAO;oBAAM,SAAS;gBAAE;gBACnC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAM,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAET,sBACC;;sCACE,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;iDAKH;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACd,CAAC,gBAAgB,SAAS,mBACzB,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;sCAGvB,8OAAC;4BAAE,WAAU;sCACV,eAAe,iCAAiC;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;;;eA7BtD;;;;;;;;;;;;;;;AAuChB;uCAEe", "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateWithAgent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport DIDAgent from \"@/components/DIDAgent\";\r\nimport DIDStreamingAvatar from \"@/components/DIDStreamingAvatar\";\r\nimport DIDLiveAvatar from \"@/components/DIDLiveAvatar\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Zap, ZapOff, Radio } from \"lucide-react\";\r\n\r\ntype AvatarMode = \"standard\" | \"streaming\" | \"live\";\r\n\r\ntype CandidateWithAgentProps = {\r\n  className?: string;\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  useAgent?: boolean;\r\n  message?: string;\r\n  onVideoReady?: () => void;\r\n  onVideoEnd?: () => void;\r\n  useStreaming?: boolean;\r\n  avatarMode?: AvatarMode;\r\n};\r\n\r\nconst CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({\r\n  className = \"\",\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  useAgent = false,\r\n  message,\r\n  onVideoReady,\r\n  onVideoEnd,\r\n  useStreaming = true,\r\n  avatarMode = \"live\", // Default to live for best real-time lip-sync\r\n}) => {\r\n  const [currentAvatarMode, setCurrentAvatarMode] = useState<AvatarMode>(\r\n    useStreaming ? avatarMode : \"standard\"\r\n  );\r\n\r\n  const cycleAvatarMode = () => {\r\n    const modes: AvatarMode[] = [\"standard\", \"streaming\", \"live\"];\r\n    const currentIndex = modes.indexOf(currentAvatarMode);\r\n    const nextIndex = (currentIndex + 1) % modes.length;\r\n    setCurrentAvatarMode(modes[nextIndex]);\r\n  };\r\n\r\n  const getAvatarModeIcon = () => {\r\n    switch (currentAvatarMode) {\r\n      case \"live\":\r\n        return <Radio className=\"w-4 h-4\" />;\r\n      case \"streaming\":\r\n        return <Zap className=\"w-4 h-4\" />;\r\n      default:\r\n        return <ZapOff className=\"w-4 h-4\" />;\r\n    }\r\n  };\r\n\r\n  const getAvatarModeLabel = () => {\r\n    switch (currentAvatarMode) {\r\n      case \"live\":\r\n        return \"Live Mode (Real-time Lip-sync)\";\r\n      case \"streaming\":\r\n        return \"Streaming Mode\";\r\n      default:\r\n        return \"Standard Mode\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* Streaming mode toggle */}\r\n      <div className=\"absolute top-2 left-2 z-10\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={toggleStreamingMode}\r\n          className=\"bg-black/20 backdrop-blur-sm hover:bg-black/30 text-white border-none\"\r\n          title={isStreamingMode ? \"Switch to Standard Mode\" : \"Switch to Live Streaming Mode\"}\r\n        >\r\n          {isStreamingMode ? (\r\n            <Zap className=\"w-4 h-4\" />\r\n          ) : (\r\n            <ZapOff className=\"w-4 h-4\" />\r\n          )}\r\n        </Button>\r\n      </div>\r\n\r\n      {useAgent ? (\r\n        isStreamingMode ? (\r\n          <DIDStreamingAvatar\r\n            className=\"w-full h-full\"\r\n            text={message}\r\n            onStreamReady={onVideoReady}\r\n            onStreamEnd={onVideoEnd}\r\n            autoStart={true}\r\n            avatarImageUrl=\"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\"\r\n          />\r\n        ) : (\r\n          <DIDAgent\r\n            className=\"w-full h-full\"\r\n            instructions={`You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`}\r\n            agentName={`${jobTitle} Interviewer`}\r\n          />\r\n        )\r\n      ) : (\r\n        <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg\">\r\n          <div className=\"text-center\">\r\n            <div className=\"w-20 h-20 bg-blue-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <span className=\"text-2xl font-bold text-blue-600\">\r\n                {candidateName.charAt(0)}\r\n              </span>\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{candidateName}</h3>\r\n            <p className=\"text-sm text-gray-600\">{jobTitle}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateWithAgent;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AANA;;;;;;;AAsBA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EAAE,EACd,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,WAAW,KAAK,EAChB,OAAO,EACP,YAAY,EACZ,UAAU,EACV,eAAe,IAAI,EACnB,aAAa,MAAM,EACpB;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,eAAe,aAAa;IAG9B,MAAM,kBAAkB;QACtB,MAAM,QAAsB;YAAC;YAAY;YAAa;SAAO;QAC7D,MAAM,eAAe,MAAM,OAAO,CAAC;QACnC,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;QACnD,qBAAqB,KAAK,CAAC,UAAU;IACvC;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;oBACV,OAAO,kBAAkB,4BAA4B;8BAEpD,gCACC,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;6CAEf,8OAAC,0MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKvB,WACC,gCACE,8OAAC,iIAAA,CAAA,UAAkB;gBACjB,WAAU;gBACV,MAAM;gBACN,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,gBAAe;;;;;qCAGjB,8OAAC,uHAAA,CAAA,UAAQ;gBACP,WAAU;gBACV,cAAc,CAAC,kEAAkE,EAAE,SAAS,eAAe,EAAE,cAAc,kGAAkG,CAAC;gBAC9N,WAAW,GAAG,SAAS,YAAY,CAAC;;;;;qCAIxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,cAAc,MAAM,CAAC;;;;;;;;;;;sCAG1B,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMlD;uCAEe", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ntype QuestionsPageProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList className=\"h-[550px]\" />\r\n          <CandidateWithAgent\r\n            className=\"h-[300px]\"\r\n            // useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"lg\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Start Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,kBAAkB;gCAClB,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,8OAAC;0BAAE;;;;;;0BACH,8OAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;uCACe", "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\n\r\ntype FinishInterviewProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\"w-[265px] h-[365px]\"\r\n            useAgent={true}\r\n            candidateName=\"<PERSON>\"\r\n            jobTitle=\"Insurance Agent\"\r\n            message=\"Thank you for completing the interview. Do you have any final questions?\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Finish Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishInterview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0CACd,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;gCACT,SAAQ;;;;;;0CAEV,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,kRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;uCAEe", "debugId": null}}, {"offset": {"line": 2069, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,8OAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;uCAEe", "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wKAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,8OAAC,mIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;4BAA8F;0CACrF,8OAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\"w-[265px] h-[265px]\"\r\n            useAgent={false} // Use static mode for analysis view\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0BACd,8OAAC,8HAAA,CAAA,UAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4HAAA,CAAA,UAAa;;;;;sCACd,8OAAC,iIAAA,CAAA,UAAkB;4BACjB,WAAU;4BACV,UAAU;4BACV,eAAc;4BACd,UAAS;;;;;;sCAEX,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,8OAAC,oIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;uCAEe", "debugId": null}}, {"offset": {"line": 2496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport CandidateWithAgent from \"../CandidateWithAgent\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateWithAgent\n            className=\"h-[300px]\"\n            candidateName=\"Jonathan\"\n            jobTitle=\"Insurance Agent\"\n          />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;uCAEe", "debugId": null}}, {"offset": {"line": 2604, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport InterviewRecording from \"../../../components/interview/InterviewRecording\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return <div>{renderCurrentComponent()}</div>;\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAeA,MAAM,YAAY;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,8OAAC,yIAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,8OAAC,8IAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAEvD,KAAK;gBACH,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBAAO,8OAAC;kBAAK;;;;;;AACf;uCAEe", "debugId": null}}]}