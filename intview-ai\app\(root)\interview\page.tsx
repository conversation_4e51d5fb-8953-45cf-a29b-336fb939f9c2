"use client";
import React, { useState } from "react";
import InterviewInstructions from "@/components/interview/InterviewInstructions";
import QuestionsPage from "@/components/interview/QuestionsPage";
import FinishInterview from "@/components/interview/FinishInterview";
import Analysis from "@/components/interview/Analysis";
import InterviewRecording from "../../../components/interview/InterviewRecording";
import QuestionsList from "@/components/QuestionsList";
import CandidateWithAgent from "@/components/CandidateWithAgent";

type InterviewStep =
  | "instructions"
  | "questions"
  | "recording"
  | "finishInterview"
  | "analysis";

const Interview = () => {
  const [currentStep, setCurrentStep] = useState<InterviewStep>("instructions");

  // const renderCurrentComponent = () => {
  //   switch (currentStep) {
  //     case "instructions":
  //       return (
  //         <InterviewInstructions onNext={() => setCurrentStep("questions")} />
  //       );
  //     case "questions":
  //       return <QuestionsPage onNext={() => setCurrentStep("recording")} />;
  //     case "recording":
  //       return (
  //         <InterviewRecording
  //           onNext={() => setCurrentStep("finishInterview")}
  //         />
  //       );
  //     case "finishInterview":
  //       return <FinishInterview onNext={() => setCurrentStep("analysis")} />;

  //     case "analysis":
  //       return <Analysis />;
  //     default:
  //       return (
  //         <InterviewInstructions onNext={() => setCurrentStep("questions")} />
  //       );
  //   }
  // };

  return <div>
    <div>
    <QuestionsList/>
    <CandidateWithAgent/>
      
       </div>
    {
      currentStep === 'recording' && <InterviewRecording/>
    }
    {
      currentStep === 'finishInterview' && <FinishInterview/>
    }
    {
      currentStep === 'analysis' && <Analysis/>
    } 
  </div>;
};

export default Interview;
