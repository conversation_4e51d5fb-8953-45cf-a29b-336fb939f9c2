import { <PERSON><PERSON><PERSON> } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import CandidateImage from "@/components/CandidateImage";
import InterviewLayout from "@/components/InterviewLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import CandidateWithAgent from "../CandidateWithAgent";

type InterviewRecordingProps = {
  onNext?: () => void;
};

const InterviewRecording = ({ onNext }: InterviewRecordingProps) => {
  return (
    <div className="h-screen">
      <JobInfoCard />

      <InterviewLayout>
        <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
          <QuestionsList className="h-[550px]" />
          <CandidateWithAgent
            className="h-[300px]"
            candidateName="Jonathan"
            jobTitle="Insurance Agent"
          />
        </div>

        <div className="flex justify-center mt-10 gap-4">
          <Button
            // disabled
            variant="default"
            className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
            onClick={() => onNext && onNext()}
          >
            Start Interview
            <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
          </Button>
        </div>
        <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
          02:00
        </div>
      </InterviewLayout>
    </div>
  );
};

export default InterviewRecording;
